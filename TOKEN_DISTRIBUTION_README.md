# Token Distribution System

This document describes the token claiming and distribution system that integrates with the PLAYrewards ecosystem.

## Overview

The token distribution system is responsible for:
1. Claiming SOL from PumpFun vaults
2. Swapping SOL to reward tokens using Jupiter
3. Validating reward amounts against USD limits
4. Creating and executing batched token transfers
5. Handling failed distributions with retry logic

## Key Components

### TokenDistributor (`src/core/token-distributor.js`)

The main class that handles all Solana/Jupiter operations:

- **Vault Claiming**: Claims SOL from PumpFun vaults
- **Token Swapping**: Uses Jupiter API to swap SOL to reward tokens
- **Price Fetching**: Gets current token prices from Jupiter
- **Reward Validation**: Caps rewards based on USD limits
- **Batch Processing**: Creates efficient transaction batches
- **Retry Logic**: Handles failed transactions with automatic retries

### Key Features

#### 1. USD-Based Reward Capping
- Players: Max $100 USD per reward (configurable via `MAX_HUNTER_REWARD_VALUE_USD`)
- Viewers: Max $50 USD per reward (configurable via `MAX_VIEWER_REWARD_VALUE_USD`)
- Automatically converts USD limits to token amounts based on current price

#### 2. Efficient Transaction Batching
- Uses Versioned Transactions (V2) for maximum efficiency
- Automatically calculates transaction sizes to stay under 1232 byte limit
- Batches multiple transfers into single transactions to minimize fees

#### 3. Token Account Validation
- Optional validation to check if recipient token accounts exist
- Skips recipients with non-existent token accounts to prevent failures
- Useful for retry scenarios after initial distribution attempts

#### 4. Comprehensive Error Handling
- Retries failed transactions up to 3 times
- Handles rate limiting from RPC endpoints
- Returns detailed results for database integration

## Environment Variables

Required environment variables:

```bash
# Solana Configuration
RPC_URL=https://api.mainnet-beta.solana.com
PK=your_private_key_in_base58
COIN=your_token_mint_address

# Reward Limits
MAX_HUNTER_REWARD_VALUE_USD=100
MAX_VIEWER_REWARD_VALUE_USD=50
```

## Integration with Rewards Processor

The TokenDistributor is designed to be integrated with `rewards-processor.js`:

### Updated Eligibility Checker
- Now stores `token_account_address` in the database
- This address is used for token transfers

### Distribution Flow
1. Rewards processor calculates SOL rewards
2. TokenDistributor claims SOL from vault
3. SOL is swapped to reward tokens via Jupiter
4. Token amounts are validated against USD limits
5. Transfers are batched and executed
6. Results are returned for database storage

## Usage Examples

### Basic Distribution
```javascript
const distributor = new TokenDistributor();
await distributor.init();

// Claim from vault
const vaultResult = await distributor.claimFromVault(vaultAddress, solAmount);

// Swap SOL to tokens
const swapResult = await distributor.swapSOLToToken(solAmount);

// Get token price
const tokenPrice = await distributor.getTokenPrice(tokenMint);

// Validate and distribute
const recipients = [...]; // Array of recipient data
const validatedRecipients = distributor.validateRewardAmounts(recipients, tokenPrice);
const batches = await distributor.createTransferBatches(validatedRecipients);
const results = await distributor.executeBatches(batches);
```

### Retry Failed Distributions
```javascript
// First run without token account validation
const batches = await distributor.createTransferBatches(recipients, false);
const results = await distributor.executeBatches(batches);

// Retry failed recipients with token account validation
const failedRecipients = results.filter(r => r.type === 'failed');
const retryBatches = await distributor.createTransferBatches(failedRecipients, true);
const retryResults = await distributor.executeBatches(retryBatches);
```

## Testing

### Test Script
Run the test script to verify functionality:

```bash
node test-token-distributor.js
```

The test script:
- Creates 100 mock recipients (70 players, 30 viewers)
- Simulates the complete distribution flow
- Tests both normal and retry scenarios
- Validates USD capping logic
- Shows transaction batching efficiency

### Example Integration
See `src/examples/token-distribution-example.js` for a complete integration example showing how to use the TokenDistributor with reward data from the rewards processor.

## API Endpoints Used

### Jupiter API
- **Quote API**: `https://lite-api.jup.ag/v6/quote` - Get swap quotes
- **Swap API**: `https://lite-api.jup.ag/v6/swap` - Get swap transactions
- **Price API**: `https://lite-api.jup.ag/price/v3` - Get token prices

### Configuration
- **Slippage**: 1% (100 basis points)
- **Max Retries**: 3 attempts per transaction
- **Rate Limiting**: Built-in delays between batches

## Database Integration

The system returns structured data for database storage:

```javascript
{
  success: true,
  vaultClaim: { amount, txid },
  swap: { inputAmount, outputAmount, txid },
  tokenPrice: 0.05,
  distribution: [
    {
      username: "Player1",
      userAddress: "...",
      tokenAccountAddress: "...",
      rewardType: "player",
      tokenAmount: 2000000,
      usdValue: 100.0,
      status: "success",
      txid: "...",
      capped: true
    }
  ]
}
```

## Security Considerations

1. **Private Key Management**: Store private keys securely
2. **RPC Rate Limiting**: Use paid RPC endpoints for production
3. **Transaction Validation**: Always validate transaction sizes
4. **Error Handling**: Implement comprehensive error logging
5. **Retry Logic**: Limit retry attempts to prevent infinite loops

## Future Enhancements

1. **Dynamic Batching**: Adjust batch sizes based on network conditions
2. **Priority Fees**: Add priority fee support for faster confirmation
3. **Multi-Token Support**: Support for multiple reward tokens
4. **Advanced Retry Logic**: Exponential backoff for failed transactions
5. **Monitoring**: Add metrics and alerting for distribution health
