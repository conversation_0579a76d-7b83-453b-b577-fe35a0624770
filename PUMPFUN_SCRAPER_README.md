# PumpFun Livestream Viewer Scraper

A robust web scraper for extracting viewer lists from PumpFun livestreams. This tool automates the process of hovering over the video, clicking the "Live" button, and extracting all participant data from the dialog that appears.

## Features

- ✅ **Cloudflare Protection**: Uses Puppeteer with stealth plugin to bypass detection
- ✅ **Automated Interaction**: Handles the complete flow (hover video → click Live button → extract data)
- ✅ **Structured Data**: Parses participants into hosts, moderators, and viewers with follower counts
- ✅ **Error Handling**: Robust error handling with debug screenshots
- ✅ **Flexible Selectors**: Multiple fallback strategies to find elements
- ✅ **Real-time Data**: Extracts live participant data as it appears
- ✅ **Continuous Monitoring**: Harvests viewer data every 5 minutes automatically
- ✅ **Audio Muted**: Browser spawns with audio completely muted
- ✅ **Persistent Session**: Keeps browser open and reuses the same session

## Installation

```bash
npm install puppeteer puppeteer-extra puppeteer-extra-plugin-stealth
```

## Usage

### Basic Usage

```javascript
const PumpFunViewerScraper = require('./pumpfun-viewer-scraper');

async function scrapeViewers() {
    const scraper = new PumpFunViewerScraper();
    
    try {
        await scraper.init();
        
        const url = 'https://pump.fun/coin/YOUR_COIN_ADDRESS_HERE';
        const result = await scraper.scrapeViewers(url);
        
        if (result.success) {
            console.log('Viewers:', result.data.participants.viewers);
            console.log('Total viewers:', result.data.totalViewers);
        }
    } finally {
        await scraper.close();
    }
}

scrapeViewers();
```

### Continuous Monitoring

```javascript
const { PumpFunViewerScraper } = require('./pumpfun-viewer-scraper');

async function startMonitoring() {
    const scraper = new PumpFunViewerScraper();

    try {
        await scraper.init();

        const url = 'https://pump.fun/coin/YOUR_COIN_ADDRESS_HERE';

        // Monitor every 5 minutes (default)
        await scraper.startContinuousMonitoring(url, 5);

    } catch (error) {
        console.error('Error:', error);
        await scraper.close();
    }
}

startMonitoring();
```

**Output Example:**
```
🕐 [9/5/2025, 7:12:55 PM] Cycle #1 - Harvesting viewer data...
✅ Successfully harvested data!
📊 Total: 21 viewers, 1 moderators, 1 hosts

👥 CURRENT VIEWERS:
  1. mayorlee0 - 0 followers
  2. ajepapi - 0 followers
  3. zizoumillz - 0 followers
  4. 3SbARf
  5. 4c3740
  6. munyanyooo - 15 followers
  7. 565db4
  8. 5d6ba9 (YOU)
  9. weedfather15 - 0 followers
  ...

⏰ Next check in 5 minutes...
```

### Run the Scraper

```bash
# Continuous monitoring (default) - harvests data every 5 minutes
node pumpfun-viewer-scraper.js

# One-time scraping only
node pumpfun-viewer-scraper.js --single

# Quick test with 30-second intervals
node quick-test-monitor.js

# Dedicated continuous monitor
node continuous-monitor.js

# Example usage (one-time scrape with data processing)
node example-usage.js
```

## Data Structure

The scraper returns structured data in the following format:

```javascript
{
  "success": true,
  "data": {
    "participants": {
      "host": [
        {
          "username": "pumpplaysgames",
          "followers": "29 followers",
          "isYou": false
        }
      ],
      "moderators": [
        {
          "username": "pm4dHy",
          "followers": "",
          "isYou": false
        }
      ],
      "viewers": [
        {
          "username": "mayorlee0",
          "followers": "0 followers",
          "isYou": false
        },
        {
          "username": "ae0c7e",
          "followers": "",
          "isYou": true
        }
        // ... more viewers
      ]
    },
    "totalViewers": 19,
    "totalModerators": 1,
    "totalHosts": 1,
    "fullText": "Stream Participants...",
    "innerHTML": "<div>...</div>"
  },
  "timestamp": "2025-09-05T17:03:43.539Z"
}
```

## How It Works

1. **Initialize Browser**: Launches Puppeteer with stealth mode and anti-detection settings
2. **Navigate to URL**: Goes to the specified PumpFun livestream page
3. **Hover Video**: Finds the `<video>` element and hovers over it to reveal controls
4. **Click Live Button**: Locates and clicks the "Live" indicator button using multiple fallback strategies
5. **Wait for Dialog**: Waits for the participants dialog to appear
6. **Extract Data**: Parses the dialog content to extract structured participant data
7. **Return Results**: Returns both raw and structured data

## Configuration

### Browser Settings

The scraper uses these browser settings for optimal performance:

- **Headless Mode**: Set to `false` by default for debugging (change to `true` for production)
- **Stealth Plugin**: Enabled to avoid Cloudflare detection
- **Custom User Agent**: Uses a realistic Chrome user agent
- **Viewport**: Set to 1920x1080 for consistent rendering
- **Audio Muted**: Browser launches with `--mute-audio` flag and JavaScript audio override
- **Persistent Session**: Browser stays open for continuous monitoring

### Error Handling

- Automatic screenshot capture on errors for debugging
- Multiple selector fallback strategies
- Graceful handling of missing elements
- Detailed error messages and logging

## Troubleshooting

### Common Issues

1. **"Live button not found"**: The livestream might not be active or the page structure changed
2. **"Video element not found"**: Page might still be loading - increase wait times
3. **Cloudflare blocking**: The stealth plugin should handle this, but you may need to adjust browser settings

### Debug Mode

Screenshots are automatically saved when errors occur. Check for files named `debug-screenshot-*.png` in your project directory.

## Legal Notice

This tool is for educational purposes only. Make sure you comply with PumpFun's terms of service and applicable laws when using this scraper. Always respect rate limits and don't overload their servers.

## License

MIT License - feel free to modify and use as needed.
