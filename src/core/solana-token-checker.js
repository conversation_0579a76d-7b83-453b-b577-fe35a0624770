require('dotenv').config();
const { Connection, PublicKey } = require('@solana/web3.js');
const { getAccount, getAssociatedTokenAddress, TOKEN_PROGRAM_ID } = require('@solana/spl-token');

class SolanaTokenChecker {
    constructor() {
        this.connection = null;
        this.tokenMint = null;
        this.minimumBalanceUI = null;
        this.rpcUrl = process.env.RPC_URL || 'https://api.mainnet-beta.solana.com';
        this.coinAddress = process.env.COIN;
        this.minimumTokenBalance = parseInt(process.env.MINIMUM_TOKEN_BALANCE_UI) || 50000;
    }

    async init() {
        try {
            console.log('🔗 Initializing Solana Token Checker...');

            // Initialize connection
            this.connection = new Connection(this.rpcUrl, 'confirmed');

            // Validate coin address
            if (!this.coinAddress) {
                throw new Error('COIN environment variable not set');
            }

            // Initialize token mint
            this.tokenMint = new PublicKey(this.coinAddress);
            this.minimumBalanceUI = this.minimumTokenBalance;

            console.log(`✅ Solana Token Checker initialized`);
            console.log(`   RPC URL: ${this.rpcUrl}`);
            console.log(`   Token Mint: ${this.coinAddress}`);
            console.log(`   Minimum Balance: ${this.minimumBalanceUI.toLocaleString()}`);

            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Solana Token Checker:', error);
            throw error;
        }
    }

    async checkTokenBalance(walletAddress) {
        try {
            if (!walletAddress || walletAddress === 'none' || walletAddress === 'N/A') {
                return {
                    success: false,
                    balance: 0, // Keep for backward compatibility
                    balanceAmount: 0,
                    balanceAmountUI: 0,
                    isEligible: false,
                    error: 'No wallet address provided'
                };
            }

            // Convert wallet address to PublicKey
            const walletPublicKey = new PublicKey(walletAddress);

            // Get the associated token account address
            const tokenAccountAddress = await getAssociatedTokenAddress(
                this.tokenMint,
                walletPublicKey,
                false, // allowOwnerOffCurve
                TOKEN_PROGRAM_ID
            );

            const tokenAccountAddressString = tokenAccountAddress.toString();

            try {
                // Get the token account info
                const tokenAccount = await getAccount(
                    this.connection,
                    tokenAccountAddress,
                    'confirmed',
                    TOKEN_PROGRAM_ID
                );

                const balanceAmount = Number(tokenAccount.amount); // Raw balance (with decimals)
                const balanceAmountUI = balanceAmount / 1_000_000; // UI balance (human readable)
                const isEligible = balanceAmountUI >= this.minimumBalanceUI;

                return {
                    success: true,
                    balance: balanceAmount, // Keep for backward compatibility
                    balanceAmount: balanceAmount,
                    balanceAmountUI: balanceAmountUI,
                    isEligible: isEligible,
                    tokenAccountAddress: tokenAccountAddressString,
                    error: null
                };

            } catch (accountError) {
                // Token account doesn't exist - user has 0 balance
                if (accountError.name === 'TokenAccountNotFoundError' ||
                    accountError.message?.includes('could not find account')) {
                    return {
                        success: true,
                        balance: 0, // Keep for backward compatibility
                        balanceAmount: 0,
                        balanceAmountUI: 0,
                        isEligible: false,
                        tokenAccountAddress: tokenAccountAddress.toString(),
                        error: null
                    };
                }
                throw accountError;
            }

        } catch (error) {
            console.error(`❌ Error checking token balance for ${walletAddress}:`, error);
            return {
                success: false,
                balance: 0, // Keep for backward compatibility
                balanceAmount: 0,
                balanceAmountUI: 0,
                isEligible: false,
                error: error.message
            };
        }
    }

    async checkMultipleBalances(addresses) {
        console.log(`🔍 Checking token balances for ${addresses.length} addresses...`);

        const results = [];
        const batchSize = 2; // Process in batches to avoid rate limiting

        for (let i = 0; i < addresses.length; i += batchSize) {
            const batch = addresses.slice(i, i + batchSize);
            const batchPromises = batch.map(async (addressInfo) => {
                const result = await this.checkTokenBalance(addressInfo.user_address);
                return {
                    ...addressInfo,
                    tokenBalance: result.balance, // Keep for backward compatibility
                    balanceAmount: result.balanceAmount,
                    balanceAmountUI: result.balanceAmountUI,
                    isEligible: result.isEligible,
                    balanceCheckError: result.error,
                    tokenAccountAddress: result.tokenAccountAddress
                };
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);

            // Small delay between batches to be respectful to RPC
            if (i + batchSize < addresses.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        const eligibleCount = results.filter(r => r.isEligible).length;
        const ineligibleCount = results.length - eligibleCount;

        console.log(`✅ Token balance check complete:`);
        console.log(`   Total checked: ${results.length}`);
        console.log(`   Eligible: ${eligibleCount}`);
        console.log(`   Ineligible: ${ineligibleCount}`);

        return results;
    }

    formatBalance(balance) {
        return balance.toLocaleString();
    }

    async testConnection() {
        try {
            const slot = await this.connection.getSlot();
            console.log(`✅ RPC connection test successful. Current slot: ${slot}`);
            return true;
        } catch (error) {
            console.error('❌ RPC connection test failed:', error);
            return false;
        }
    }

    async close() {
        // No explicit cleanup needed for Connection
        console.log('🔒 Solana Token Checker closed');
    }
}

module.exports = SolanaTokenChecker;
