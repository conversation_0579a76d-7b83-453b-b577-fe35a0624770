const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const Database = require('./database');

// Use stealth plugin to avoid detection
puppeteer.use(StealthPlugin());

class PumpFunViewerScraper {
    constructor(enableDatabase = true) {
        this.browser = null;
        this.page = null;
        this.userAddressPage = null; // Second page for user address fetching
        this.enableDatabase = enableDatabase;
        this.database = null;
        this.userAddressQueue = [];
        this.isProcessingUserAddresses = false;
        this.userAddressFetchInterval = 5 * 60 * 1000;
        this.userAddressTimer = null;
    }

    async initDatabase() {
        if (this.enableDatabase) {
            try {
                this.database = new Database();
                await this.database.init();
                console.log('✅ Database initialized for viewer scraper');
            } catch (error) {
                console.error('❌ Failed to initialize database:', error);
                this.enableDatabase = false;
            }
        }
    }

    async init() {
        console.log('Initializing browser...');
        this.browser = await puppeteer.launch({
            headless: false, // Set to true for production
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--mute-audio',
                '--autoplay-policy=no-user-gesture-required'
            ]
        });

        this.page = await this.browser.newPage();

        // Set user agent to avoid detection
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Set viewport
        await this.page.setViewport({ width: 1920, height: 1080 });

        // Mute audio on the page level as well
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(HTMLMediaElement.prototype, 'volume', {
                set: () => { },
                get: () => 0
            });
            Object.defineProperty(HTMLMediaElement.prototype, 'muted', {
                set: () => { },
                get: () => true
            });
        });

        // Create second page for user address fetching (background, never focused)
        this.userAddressPage = await this.browser.newPage();
        await this.userAddressPage.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        await this.userAddressPage.setViewport({ width: 1280, height: 720 });

        // Mute audio on user address page too
        await this.userAddressPage.evaluateOnNewDocument(() => {
            Object.defineProperty(HTMLMediaElement.prototype, 'volume', {
                set: () => { },
                get: () => 0
            });
            Object.defineProperty(HTMLMediaElement.prototype, 'muted', {
                set: () => { },
                get: () => true
            });
        });

        // Ensure the main pump.fun page stays in focus
        await this.page.bringToFront();

        console.log('Browser initialized successfully with audio muted and background user address page created');
    }

    async handleInitialPageLoad(url) {
        console.log(`🌐 Navigating to: ${url}`);
        await this.page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: 60000 // Increased timeout to be patient
        });

        // Wait for page to load completely
        console.log('⏳ Waiting for page to fully load...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Look for and click the "I'm ready to pump" button with retry logic
        await this.handleEntryButton();

        // Additional wait to ensure everything is loaded
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    async handleEntryButton() {
        console.log('🔍 Looking for "I\'m ready to pump" button...');

        const maxRetries = 3;
        const retryDelay = 3000; // 3 seconds between retries

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Ensure pump.fun tab is in focus
                await this.page.bringToFront();

                console.log(`🔄 Attempt ${attempt}/${maxRetries} to find entry button...`);

                const readyButton = await this.page.waitForSelector(
                    'button[data-test-id="how-it-works-button"]',
                    { timeout: 10000 }
                );

                if (readyButton) {
                    console.log('✅ Found "I\'m ready to pump" button, clicking...');
                    await readyButton.click();

                    // Wait for the page to transition after clicking
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    console.log('🎯 Successfully entered the website');
                    return; // Success, exit the retry loop
                }
            } catch (error) {
                console.log(`⚠️  Attempt ${attempt}/${maxRetries} failed: ${error.message}`);

                if (attempt < maxRetries) {
                    console.log(`⏳ Waiting ${retryDelay / 1000} seconds before retry...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                } else {
                    // Final attempt failed, try refreshing the page
                    console.log('🔄 All attempts failed, refreshing page and trying once more...');
                    try {
                        await this.page.reload({ waitUntil: 'networkidle2', timeout: 60000 });
                        await new Promise(resolve => setTimeout(resolve, 5000));

                        // One final attempt after refresh
                        const readyButton = await this.page.waitForSelector(
                            'button[data-test-id="how-it-works-button"]',
                            { timeout: 10000 }
                        );

                        if (readyButton) {
                            console.log('✅ Found button after refresh, clicking...');
                            await readyButton.click();
                            await new Promise(resolve => setTimeout(resolve, 3000));
                            console.log('🎯 Successfully entered the website after refresh');
                            return;
                        }
                    } catch (refreshError) {
                        console.log('❌ Refresh attempt also failed:', refreshError.message);
                    }

                    console.log('ℹ️  Entry button not found after all attempts, continuing anyway...');
                }
            }
        }
    }

    async scrapeViewers(url) {
        try {
            await this.handleInitialPageLoad(url);

            // Step 1: Find and hover over the video element
            console.log('Looking for video element...');
            await this.page.waitForSelector('video', { timeout: 10000 });

            const videoElement = await this.page.$('video');
            if (!videoElement) {
                throw new Error('Video element not found');
            }

            console.log('Hovering over video...');
            await videoElement.hover();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Step 2: Find and click the "Live" button
            console.log('Looking for Live button...');

            // Try multiple selectors to find the live button
            const liveButtonSelectors = [
                '#live-indicator',
                'div[id="live-indicator"]',
                'div:has-text("Live")',
                'div.bg-livestream-green',
                'div[class*="bg-livestream-green"]'
            ];

            let liveButton = null;
            for (const selector of liveButtonSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 2000 });
                    liveButton = await this.page.$(selector);
                    if (liveButton) {
                        console.log(`Found live button with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    // Continue to next selector
                }
            }

            // If not found by selector, try finding by text content
            if (!liveButton) {
                console.log('Trying to find Live button by text content...');
                liveButton = await this.page.evaluateHandle(() => {
                    const elements = Array.from(document.querySelectorAll('div'));
                    return elements.find(el =>
                        el.textContent.toLowerCase().includes('live') &&
                        el.offsetParent !== null // element is visible
                    );
                });
            }

            if (!liveButton || liveButton.asElement() === null) {
                throw new Error('Live button not found');
            }

            console.log('Clicking Live button...');
            await liveButton.asElement().click();
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Step 3: Wait for the dialog to appear and find viewers section
            console.log('Waiting for dialog to appear...');
            await this.page.waitForSelector('div[role="dialog"]', { timeout: 10000 });

            // Step 4: Look for the "Viewers" text element
            console.log('Looking for Viewers section...');

            const viewersElement = await this.page.evaluateHandle(() => {
                const elements = Array.from(document.querySelectorAll('div'));
                return elements.find(el =>
                    el.textContent.toLowerCase().includes('viewers') &&
                    el.classList.contains('text-xs') &&
                    el.classList.contains('font-bold')
                );
            });

            if (!viewersElement || viewersElement.asElement() === null) {
                throw new Error('Viewers section not found');
            }

            // Step 5: Extract all text content from the dialog
            console.log('Extracting viewer data from dialog...');

            const dialogContent = await this.page.evaluate(() => {
                const dialog = document.querySelector('div[role="dialog"]');
                if (!dialog) return null;

                // Function to extract all text content recursively
                function extractTextContent(element) {
                    let text = '';
                    for (const node of element.childNodes) {
                        if (node.nodeType === Node.TEXT_NODE) {
                            text += node.textContent.trim() + ' ';
                        } else if (node.nodeType === Node.ELEMENT_NODE) {
                            text += extractTextContent(node) + ' ';
                        }
                    }
                    return text;
                }

                // Parse structured data
                const participants = {
                    host: [],
                    moderators: [],
                    viewers: []
                };

                // Find all user entries
                const userElements = dialog.querySelectorAll('div[class*="flex h-[26px] items-center justify-between gap-2"]');

                userElements.forEach(element => {
                    const usernameEl = element.querySelector('p[class*="truncate whitespace-nowrap text-xs font-bold"]');
                    const followersEl = element.querySelector('p[class*="text-xs text-livestream-muted"]');

                    if (usernameEl) {
                        const username = usernameEl.textContent.trim();
                        const followers = followersEl ? followersEl.textContent.trim() : '';
                        const isYou = element.textContent.includes('(you)');

                        const userData = {
                            username,
                            followers,
                            isYou
                        };

                        // Determine section based on position in DOM
                        const parentSection = element.closest('div[class*="flex max-w-xs flex-col gap-2"]');
                        if (parentSection) {
                            const sectionHeader = parentSection.querySelector('div[class*="text-xs font-bold lowercase text-livestream-muted first-letter:uppercase"]');
                            if (sectionHeader) {
                                const sectionText = sectionHeader.textContent.toLowerCase();
                                if (sectionText.includes('host')) {
                                    participants.host.push(userData);
                                } else if (sectionText.includes('moderators')) {
                                    participants.moderators.push(userData);
                                } else if (sectionText.includes('viewers')) {
                                    participants.viewers.push(userData);
                                }
                            }
                        }
                    }
                });

                return {
                    fullText: extractTextContent(dialog),
                    innerHTML: dialog.innerHTML,
                    participants: participants,
                    totalViewers: participants.viewers.length,
                    totalModerators: participants.moderators.length,
                    totalHosts: participants.host.length
                };
            });

            if (!dialogContent) {
                throw new Error('Could not extract dialog content');
            }

            console.log('Successfully extracted viewer data!');
            return {
                success: true,
                data: dialogContent,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('Error during scraping:', error.message);

            // Take a screenshot for debugging
            try {
                await this.page.screenshot({
                    path: `debug-screenshot-${Date.now()}.png`,
                    fullPage: true
                });
                console.log('Debug screenshot saved');
            } catch (screenshotError) {
                console.error('Could not save screenshot:', screenshotError.message);
            }

            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    async startContinuousMonitoring(url, intervalMinutes = 5, enableUserAddressFetching = true) {
        console.log(`🔄 Starting continuous monitoring every ${intervalMinutes} minutes...`);
        console.log(`🔍 User address fetching: ${enableUserAddressFetching ? 'Enabled' : 'Disabled'}`);
        console.log('Press Ctrl+C to stop monitoring\n');

        // Initial navigation and setup
        await this.handleInitialPageLoad(url);
        await this.setupInitialState();

        // Start user address fetching if enabled
        if (enableUserAddressFetching) {
            this.startUserAddressFetching();
        }

        let cycleCount = 0;

        // Set up the monitoring loop
        const monitoringInterval = setInterval(async () => {
            try {
                cycleCount++;
                const timestamp = new Date().toLocaleString();
                console.log(`\n🕐 [${timestamp}] Cycle #${cycleCount} - Harvesting viewer data...`);

                const result = await this.harvestViewerData();

                if (result.success) {
                    const { totalViewers, totalModerators, totalHosts } = result.data;

                    // Check if we got 0 results - might need to refresh
                    if (totalViewers === 0 && totalModerators === 0 && totalHosts === 0) {
                        console.log('⚠️  Got 0 viewers/moderators/hosts - refreshing page...');
                        try {
                            await this.handleInitialPageLoad(url);
                            await this.setupInitialState();
                            console.log('🔄 Page refreshed, will try again in next cycle');
                        } catch (refreshError) {
                            console.error('❌ Failed to refresh page:', refreshError.message);
                        }
                    } else {
                        console.log(`✅ Successfully harvested data!`);
                        console.log(`📊 Total: ${totalViewers} viewers, ${totalModerators} moderators, ${totalHosts} hosts`);

                        // Print all viewers to console
                        console.log('\n👥 CURRENT VIEWERS:');
                        result.data.participants.viewers.forEach((viewer, index) => {
                            const youIndicator = viewer.isYou ? ' (YOU)' : '';
                            const followers = viewer.followers ? ` - ${viewer.followers}` : '';
                            console.log(`  ${index + 1}. ${viewer.username}${followers}${youIndicator}`);
                        });
                    }

                    console.log(`\n⏰ Next check in ${intervalMinutes} minutes...`);
                } else {
                    console.error(`❌ Failed to harvest data: ${result.error}`);
                    console.log('🔄 Will retry in next cycle...');
                }

            } catch (error) {
                console.error(`💥 Error during monitoring cycle: ${error.message}`);
                console.log('🔄 Will retry in next cycle...');
            }
        }, intervalMinutes * 60 * 1000);

        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Stopping monitoring...');
            clearInterval(monitoringInterval);
            this.close();
            process.exit(0);
        });

        // Keep the process alive
        return new Promise(() => { }); // Never resolves, keeps running until SIGINT
    }

    async setupInitialState() {
        try {
            // Find and hover over the video element
            console.log('🎥 Setting up initial state - hovering over video...');
            await this.page.waitForSelector('video', { timeout: 10000 });

            const videoElement = await this.page.$('video');
            if (videoElement) {
                await videoElement.hover();
                await new Promise(resolve => setTimeout(resolve, 1000));
                console.log('✅ Initial setup complete');
            }
        } catch (error) {
            console.log('⚠️ Warning: Could not complete initial setup:', error.message);
        }
    }

    async harvestViewerData() {
        try {
            // Click the Live button to open the dialog
            const liveButtonSelectors = [
                '#live-indicator',
                'div[id="live-indicator"]',
                'div:has-text("Live")',
                'div.bg-livestream-green',
                'div[class*="bg-livestream-green"]'
            ];

            let liveButton = null;
            for (const selector of liveButtonSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 2000 });
                    liveButton = await this.page.$(selector);
                    if (liveButton) break;
                } catch (e) {
                    // Continue to next selector
                }
            }

            // If not found by selector, try finding by text content
            if (!liveButton) {
                liveButton = await this.page.evaluateHandle(() => {
                    const elements = Array.from(document.querySelectorAll('div'));
                    return elements.find(el =>
                        el.textContent.toLowerCase().includes('live') &&
                        el.offsetParent !== null
                    );
                });
            }

            if (!liveButton || liveButton.asElement() === null) {
                throw new Error('Live button not found');
            }

            await liveButton.asElement().click();
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Wait for dialog and extract data
            await this.page.waitForSelector('div[role="dialog"]', { timeout: 10000 });

            const dialogContent = await this.page.evaluate(() => {
                const dialog = document.querySelector('div[role="dialog"]');
                if (!dialog) return null;

                // Function to extract all text content recursively
                function extractTextContent(element) {
                    let text = '';
                    for (const node of element.childNodes) {
                        if (node.nodeType === Node.TEXT_NODE) {
                            text += node.textContent.trim() + ' ';
                        } else if (node.nodeType === Node.ELEMENT_NODE) {
                            text += extractTextContent(node) + ' ';
                        }
                    }
                    return text;
                }

                // Parse structured data
                const participants = {
                    host: [],
                    moderators: [],
                    viewers: []
                };

                // Find all user entries
                const userElements = dialog.querySelectorAll('div[class*="flex h-[26px] items-center justify-between gap-2"]');

                userElements.forEach(element => {
                    const usernameEl = element.querySelector('p[class*="truncate whitespace-nowrap text-xs font-bold"]');
                    const followersEl = element.querySelector('p[class*="text-xs text-livestream-muted"]');

                    if (usernameEl) {
                        const username = usernameEl.textContent.trim();
                        const followers = followersEl ? followersEl.textContent.trim() : '';
                        const isYou = element.textContent.includes('(you)');

                        const userData = {
                            username,
                            followers,
                            isYou
                        };

                        // Determine section based on position in DOM
                        const parentSection = element.closest('div[class*="flex max-w-xs flex-col gap-2"]');
                        if (parentSection) {
                            const sectionHeader = parentSection.querySelector('div[class*="text-xs font-bold lowercase text-livestream-muted first-letter:uppercase"]');
                            if (sectionHeader) {
                                const sectionText = sectionHeader.textContent.toLowerCase();
                                if (sectionText.includes('host')) {
                                    participants.host.push(userData);
                                } else if (sectionText.includes('moderators')) {
                                    participants.moderators.push(userData);
                                } else if (sectionText.includes('viewers')) {
                                    participants.viewers.push(userData);
                                }
                            }
                        }
                    }
                });

                return {
                    fullText: extractTextContent(dialog),
                    participants: participants,
                    totalViewers: participants.viewers.length,
                    totalModerators: participants.moderators.length,
                    totalHosts: participants.host.length
                };
            });

            if (!dialogContent) {
                throw new Error('Could not extract dialog content');
            }

            // Close the dialog by clicking outside or pressing escape
            await this.page.keyboard.press('Escape');
            await new Promise(resolve => setTimeout(resolve, 500));

            // Store in database if enabled
            if (this.enableDatabase && this.database) {
                try {
                    const sessionId = `scrape_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                    await this.database.insertViewerSession({
                        participants: dialogContent.participants,
                        timestamp: new Date().toISOString(),
                        sessionId: sessionId
                    });
                } catch (error) {
                    console.error('Error storing viewer data in database:', error);
                }
            }

            return {
                success: true,
                data: dialogContent,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // User address fetching methods
    async fetchUserAddress(username) {
        if (!this.userAddressPage) {
            throw new Error('User address page not initialized');
        }

        try {
            const apiUrl = `https://frontend-api-v3.pump.fun/users/${encodeURIComponent(username)}`;
            console.log(`🔍 Fetching address for user: ${username}`);

            // Navigate directly to the API URL - browser will show JSON response
            await this.userAddressPage.goto(apiUrl, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Wait a bit for page to load
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Extract the JSON response from the page
            const result = await this.userAddressPage.evaluate(() => {
                try {
                    // Get the page content (should be JSON)
                    const pageText = document.body.innerText || document.body.textContent;

                    // Check if the pageText is empty, if so, there is no address.
                    if (!pageText.trim()) {
                        return { success: true, address: 'none', userData: null };
                    }

                    // Parse the JSON response
                    const jsonData = JSON.parse(pageText);

                    // Return the address field
                    return {
                        success: true,
                        address: jsonData.address || null,
                        userData: jsonData
                    };
                } catch (parseError) {
                    // If parsing fails, check if it's a 404 or other error
                    const pageText = document.body.innerText || document.body.textContent;
                    if (pageText.includes('404') || pageText.includes('Not Found')) {
                        return { success: true, address: 'none', userData: null };
                    }
                    return {
                        success: false,
                        error: `Parse error: ${parseError.message}`,
                        pageContent: pageText.substring(0, 200) // First 200 chars for debugging
                    };
                }
            });

            if (result.success) {
                if (result.address && result.address !== 'none') {
                    console.log(`✅ Found address for ${username}: ${result.address}`);
                } else {
                    console.log(`ℹ️  No address found for ${username}, marked as 'none'`);
                }
                return { success: true, address: result.address || 'none' };
            } else {
                console.error(`❌ Failed to parse response for ${username}: ${result.error}`);
                return { success: false, error: result.error, address: null };
            }

        } catch (error) {
            console.error(`❌ Error fetching address for ${username}:`, error.message);
            return { success: false, error: error.message, address: null };
        } finally {
            // Always ensure the main pump.fun page stays in focus
            try {
                await this.page.bringToFront();
            } catch (focusError) {
                console.log('⚠️  Could not bring main page to front:', focusError.message);
            }
        }
    }

    async processUserAddressQueue() {
        if (this.isProcessingUserAddresses || this.userAddressQueue.length === 0) {
            return;
        }

        this.isProcessingUserAddresses = true;
        console.log(`🔄 Processing ${this.userAddressQueue.length} users in address queue...`);

        while (this.userAddressQueue.length > 0) {
            const username = this.userAddressQueue.shift();

            try {
                const result = await this.fetchUserAddress(username);

                if (result.success && this.enableDatabase && this.database) {
                    await this.database.updateUserAddress(username, result.address);
                }

                // Rate limiting - wait 2 seconds between requests to be respectful
                await new Promise(resolve => setTimeout(resolve, 2000));

            } catch (error) {
                console.error(`❌ Error processing user ${username}:`, error);
            }
        }

        this.isProcessingUserAddresses = false;
        console.log('✅ User address queue processing completed');
    }

    async fetchMissingUserAddresses() {
        if (!this.enableDatabase || !this.database) {
            console.log('ℹ️  Database not enabled, skipping user address fetch');
            return;
        }

        try {
            const usersWithoutAddress = await this.database.getUsersWithoutAddress(20); // Fetch 20 at a time

            if (usersWithoutAddress.length === 0) {
                console.log('ℹ️  No users without addresses found');
                return;
            }

            console.log(`📋 Found ${usersWithoutAddress.length} users without addresses`);

            // Add users to queue
            for (const user of usersWithoutAddress) {
                if (!this.userAddressQueue.includes(user.username)) {
                    this.userAddressQueue.push(user.username);
                }
            }

            // Process the queue
            await this.processUserAddressQueue();

        } catch (error) {
            console.error('❌ Error fetching missing addresses:', error);
        }
    }

    startUserAddressFetching() {
        // Initial fetch
        this.fetchMissingUserAddresses().catch(error => {
            console.error('❌ Initial user address fetch failed:', error.message);
        });

        // Set up periodic fetching
        this.userAddressTimer = setInterval(async () => {
            try {
                const timestamp = new Date().toLocaleString();
                console.log(`\n📧 [${timestamp}] Fetching missing user addresses...`);

                await this.fetchMissingUserAddresses();

                console.log(`📊 Queue status: ${this.userAddressQueue.length} pending`);
                console.log(`⏰ Next address fetch in ${this.userAddressFetchInterval / 60000} minutes`);
            } catch (error) {
                console.error(`💥 Error during user address fetching: ${error.message}`);
            }
        }, this.userAddressFetchInterval);

        console.log('🚀 User address fetching started');
    }

    stopUserAddressFetching() {
        if (this.userAddressTimer) {
            clearInterval(this.userAddressTimer);
            this.userAddressTimer = null;
            console.log('🛑 User address fetching stopped');
        }
    }

    async close() {
        // Stop user address fetching
        this.stopUserAddressFetching();

        if (this.browser) {
            await this.browser.close();
            console.log('Browser closed');
        }

        if (this.database) {
            await this.database.close();
            this.database = null;
        }
    }
}

// Main execution function for continuous monitoring
async function main() {
    const scraper = new PumpFunViewerScraper();

    try {
        await scraper.init();
        await scraper.initDatabase();

        const url = 'https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump';

        console.log('🚀 Starting PumpFun Continuous Viewer Monitor');
        console.log('🔇 Audio is muted');
        console.log('📊 Will harvest viewer data every 5 minutes');
        console.log('🛑 Press Ctrl+C to stop\n');

        // Start continuous monitoring (runs forever until Ctrl+C)
        await scraper.startContinuousMonitoring(url, 5);

    } catch (error) {
        console.error('💥 Fatal error:', error);
        await scraper.close();
    }
}

// One-time scraping function (for backwards compatibility)
async function singleScrape() {
    const scraper = new PumpFunViewerScraper();

    try {
        await scraper.init();
        await scraper.initDatabase();

        const url = 'https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump';
        const result = await scraper.scrapeViewers(url);

        console.log('\n=== SCRAPING RESULT ===');
        console.log(JSON.stringify(result, null, 2));

        if (result.success) {
            console.log('\n=== STRUCTURED PARTICIPANT DATA ===');
            console.log(`Total Hosts: ${result.data.totalHosts}`);
            console.log(`Total Moderators: ${result.data.totalModerators}`);
            console.log(`Total Viewers: ${result.data.totalViewers}`);

            console.log('\n--- HOSTS ---');
            result.data.participants.host.forEach(user => {
                console.log(`${user.username} ${user.followers ? `(${user.followers})` : ''} ${user.isYou ? '(YOU)' : ''}`);
            });

            console.log('\n--- MODERATORS ---');
            result.data.participants.moderators.forEach(user => {
                console.log(`${user.username} ${user.followers ? `(${user.followers})` : ''} ${user.isYou ? '(YOU)' : ''}`);
            });

            console.log('\n--- VIEWERS ---');
            result.data.participants.viewers.forEach(user => {
                console.log(`${user.username} ${user.followers ? `(${user.followers})` : ''} ${user.isYou ? '(YOU)' : ''}`);
            });

            console.log('\n=== RAW TEXT DATA ===');
            console.log(result.data.fullText);
        }

    } catch (error) {
        console.error('Fatal error:', error);
    } finally {
        await scraper.close();
    }
}

// Export for use as module
module.exports = {
    PumpFunViewerScraper,
    main,
    singleScrape
};

// Run if called directly
if (require.main === module) {
    // Check command line arguments
    const args = process.argv.slice(2);
    if (args.includes('--single')) {
        singleScrape();
    } else {
        main(); // Default to continuous monitoring
    }
}
