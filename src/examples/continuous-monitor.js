const { PumpFunViewerScraper } = require('../core/pumpfun-viewer-scraper');

async function startMonitoring() {
    const scraper = new PumpFunViewerScraper();

    try {
        console.log('🎯 PumpFun Continuous Viewer Monitor');
        console.log('=====================================');
        console.log('🔇 Audio: MUTED');
        console.log('⏱️  Interval: Every 5 minutes');
        console.log('🛑 Stop: Press Ctrl+C');
        console.log('=====================================\n');

        await scraper.init();

        // You can change this URL to any PumpFun livestream
        const url = 'https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump';

        // Start continuous monitoring
        await scraper.startContinuousMonitoring(url, 5);

    } catch (error) {
        console.error('💥 Fatal error:', error);
        await scraper.close();
        process.exit(1);
    }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Start monitoring
if (require.main === module) {
    startMonitoring();
}

module.exports = startMonitoring;
