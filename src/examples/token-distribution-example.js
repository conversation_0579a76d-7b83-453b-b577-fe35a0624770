require('dotenv').config();
const TokenDistributor = require('../core/token-distributor');

/**
 * Example integration showing how TokenDistributor would work with rewards-processor.js
 * This demonstrates the complete flow from reward calculation to token distribution
 */
class TokenDistributionExample {
    constructor() {
        this.distributor = new TokenDistributor();
    }

    async init() {
        await this.distributor.init();
    }

    /**
     * Main distribution flow that would be called from rewards-processor.js
     * @param {Object} rewardData - Data from reward calculation
     * @param {Array} rewardData.playerRewards - Player reward data
     * @param {Array} rewardData.viewerRewards - Viewer reward data
     * @param {number} rewardData.totalSOL - Total SOL available for rewards
     * @param {boolean} checkTokenAccounts - Whether to validate token accounts exist
     */
    async distributeRewards(rewardData, checkTokenAccounts = false) {
        try {
            console.log('🎯 Starting token distribution process...\n');

            const { playerRewards, viewerRewards, totalSOL } = rewardData;
            
            // Step 1: Claim SOL from vault (if needed)
            console.log('🏦 Step 1: Claiming <PERSON>OL from vault...');
            const vaultResult = await this.distributor.claimFromVault('vault_address', totalSOL);
            console.log(`✅ Claimed ${vaultResult.amount} SOL from vault\n`);

            // Step 2: Swap SOL to tokens
            console.log('🔄 Step 2: Swapping SOL to reward tokens...');
            const swapResult = await this.distributor.swapSOLToToken(totalSOL);
            console.log(`✅ Swapped ${swapResult.inputAmount} SOL for ${(swapResult.outputAmount / 1e6).toLocaleString()} tokens\n`);

            // Step 3: Get current token price for USD validation
            console.log('💰 Step 3: Getting token price...');
            const tokenPrice = await this.distributor.getTokenPrice(process.env.COIN);
            console.log(`✅ Current token price: $${tokenPrice}\n`);

            // Step 4: Convert reward data to distribution format
            console.log('📊 Step 4: Converting rewards to distribution format...');
            const recipients = this.convertRewardsToRecipients(playerRewards, viewerRewards, swapResult.outputAmount);
            console.log(`✅ Prepared ${recipients.length} recipients for distribution\n`);

            // Step 5: Validate reward amounts against USD limits
            console.log('🔍 Step 5: Validating reward amounts...');
            const validatedRecipients = this.distributor.validateRewardAmounts(recipients, tokenPrice);
            console.log(`✅ Validation complete\n`);

            // Step 6: Create and execute transfer batches
            console.log('📦 Step 6: Creating transfer batches...');
            const batches = await this.distributor.createTransferBatches(validatedRecipients, checkTokenAccounts);
            console.log(`✅ Created ${batches.length} batches\n`);

            // Step 7: Execute batches
            console.log('🚀 Step 7: Executing transfer batches...');
            const results = await this.distributor.executeBatches(batches);
            console.log(`✅ Distribution complete\n`);

            // Step 8: Format results for database storage
            const formattedResults = this.formatResultsForDatabase(results, tokenPrice);

            return {
                success: true,
                vaultClaim: vaultResult,
                swap: swapResult,
                tokenPrice: tokenPrice,
                distribution: formattedResults,
                summary: this.generateSummary(results)
            };

        } catch (error) {
            console.error('❌ Token distribution failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Convert player and viewer rewards to recipient format
     */
    convertRewardsToRecipients(playerRewards, viewerRewards, totalTokens) {
        const recipients = [];
        
        // Calculate total SOL rewards to determine token allocation
        const totalSOLRewards = [...playerRewards, ...viewerRewards]
            .reduce((sum, reward) => sum + reward.rewardSOL, 0);

        // Convert player rewards
        playerRewards.forEach(player => {
            const tokenAmount = Math.floor((player.rewardSOL / totalSOLRewards) * totalTokens);
            recipients.push({
                username: player.username,
                userAddress: player.user_address,
                tokenAccountAddress: player.token_account_address, // From eligibility data
                rewardType: 'player',
                tokenAmount: tokenAmount,
                solAmount: player.rewardSOL,
                percentage: player.percentage,
                rank: player.rank,
                moves: player.moves
            });
        });

        // Convert viewer rewards
        viewerRewards.forEach(viewer => {
            const tokenAmount = Math.floor((viewer.rewardSOL / totalSOLRewards) * totalTokens);
            recipients.push({
                username: viewer.username,
                userAddress: viewer.user_address,
                tokenAccountAddress: viewer.token_account_address, // From eligibility data
                rewardType: 'viewer',
                tokenAmount: tokenAmount,
                solAmount: viewer.rewardSOL,
                percentage: viewer.percentage,
                rank: viewer.rank,
                viewingSessions: viewer.viewing_sessions
            });
        });

        return recipients;
    }

    /**
     * Format distribution results for database storage
     */
    formatResultsForDatabase(results, tokenPrice) {
        const formattedResults = [];

        results.forEach(result => {
            if (result.type === 'success') {
                // Successful batch - mark all recipients as rewarded
                result.recipients.forEach(recipient => {
                    formattedResults.push({
                        username: recipient.username,
                        userAddress: recipient.userAddress,
                        tokenAccountAddress: recipient.tokenAccountAddress,
                        rewardType: recipient.rewardType,
                        tokenAmount: recipient.tokenAmount,
                        usdValue: (recipient.tokenAmount / 1e6) * tokenPrice,
                        status: 'success',
                        txid: result.txid,
                        error: null,
                        capped: recipient.capped || false
                    });
                });
            } else {
                // Failed recipient
                formattedResults.push({
                    username: result.recipient.username,
                    userAddress: result.recipient.userAddress,
                    tokenAccountAddress: result.recipient.tokenAccountAddress,
                    rewardType: result.recipient.rewardType,
                    tokenAmount: result.recipient.tokenAmount,
                    usdValue: (result.recipient.tokenAmount / 1e6) * tokenPrice,
                    status: 'failed',
                    txid: null,
                    error: result.reason,
                    capped: result.recipient.capped || false
                });
            }
        });

        return formattedResults;
    }

    /**
     * Generate summary statistics
     */
    generateSummary(results) {
        const successful = results.filter(r => r.type === 'success');
        const failed = results.filter(r => r.type === 'failed');
        
        const totalSuccessfulRecipients = successful.reduce((sum, batch) => 
            sum + (batch.recipients ? batch.recipients.length : 0), 0);
        
        const totalFailedRecipients = failed.length;
        
        const totalTokensDistributed = successful.reduce((sum, batch) => {
            if (batch.recipients) {
                return sum + batch.recipients.reduce((batchSum, recipient) => 
                    batchSum + recipient.tokenAmount, 0);
            }
            return sum;
        }, 0);

        return {
            totalBatches: results.length,
            successfulBatches: successful.length,
            failedBatches: failed.length,
            totalSuccessfulRecipients,
            totalFailedRecipients,
            totalTokensDistributed: totalTokensDistributed,
            totalTokensDistributedUI: totalTokensDistributed / 1e6
        };
    }

    /**
     * Handle failed distributions - retry with token account validation
     */
    async retryFailedDistributions(failedRecipients) {
        console.log(`🔄 Retrying ${failedRecipients.length} failed distributions with token account validation...`);
        
        const retryBatches = await this.distributor.createTransferBatches(failedRecipients, true);
        const retryResults = await this.distributor.executeBatches(retryBatches);
        
        return retryResults;
    }
}

// Example usage function
async function exampleUsage() {
    console.log('🧪 Token Distribution Example\n');

    try {
        const example = new TokenDistributionExample();
        await example.init();

        // Mock reward data (this would come from rewards-processor.js)
        const mockRewardData = {
            totalSOL: 5.0,
            playerRewards: [
                {
                    username: 'Player1',
                    user_address: 'FKWsQNbuTN3bXY3mTra66nNb8x61wDTKcdQYf184mUk1',
                    token_account_address: '4Ebcm2jSeRDZeRV13bmzvwgk7rGYMXU19qbPq9qEW6nG',
                    rewardSOL: 2.0,
                    percentage: 40,
                    rank: 1,
                    moves: 150
                },
                {
                    username: 'Player2',
                    user_address: 'FKWsQNbuTN3bXY3mTra66nNb8x61wDTKcdQYf184mUk1',
                    token_account_address: '4Ebcm2jSeRDZeRV13bmzvwgk7rGYMXU19qbPq9qEW6nG',
                    rewardSOL: 1.5,
                    percentage: 30,
                    rank: 2,
                    moves: 120
                }
            ],
            viewerRewards: [
                {
                    username: 'Viewer1',
                    user_address: 'FKWsQNbuTN3bXY3mTra66nNb8x61wDTKcdQYf184mUk1',
                    token_account_address: '4Ebcm2jSeRDZeRV13bmzvwgk7rGYMXU19qbPq9qEW6nG',
                    rewardSOL: 0.75,
                    percentage: 15,
                    rank: 1,
                    viewing_sessions: 5
                },
                {
                    username: 'Viewer2',
                    user_address: 'FKWsQNbuTN3bXY3mTra66nNb8x61wDTKcdQYf184mUk1',
                    token_account_address: '4Ebcm2jSeRDZeRV13bmzvwgk7rGYMXU19qbPq9qEW6nG',
                    rewardSOL: 0.75,
                    percentage: 15,
                    rank: 2,
                    viewing_sessions: 3
                }
            ]
        };

        // Run distribution
        const result = await example.distributeRewards(mockRewardData, false);
        
        if (result.success) {
            console.log('🎉 Distribution completed successfully!');
            console.log('📊 Summary:', result.summary);
        } else {
            console.log('💥 Distribution failed:', result.error);
        }

    } catch (error) {
        console.error('💥 Example failed:', error);
    }
}

// Run example if this file is executed directly
if (require.main === module) {
    exampleUsage();
}

module.exports = TokenDistributionExample;
