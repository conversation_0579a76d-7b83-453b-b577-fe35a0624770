const PumpFunWebSocketClient = require('./websocket-client');

class PLAYRewards {
    constructor() {
        this.wsClient = null;
        this.players = new Map(); // userId -> { moves: [], joinTime: timestamp }
        this.viewers = new Set();
        this.isRunning = false;
        this.backgroundTasks = [];
    }

    async start() {
        // Get room ID from LIVESTREAM_COIN
        const roomId = process.env.LIVESTREAM_COIN;
        if (!roomId) {
            throw new Error('LIVESTREAM_COIN not found in .env');
        }

        this.wsClient = new PumpFunWebSocketClient(roomId);

        // Set up event listeners using EventEmitter pattern
        this.wsClient.on('userJoined', (data) => this.handleUserJoined(data));
        this.wsClient.on('userLeft', (data) => this.handleUserLeft(data));
        this.wsClient.on('validMove', (data) => this.handleValidMove(data));

        try {
            await this.wsClient.connect();
            this.isRunning = true;
            this.startBackgroundTasks();
            console.log('PLAYRewards system started successfully');
        } catch (error) {
            console.error('Failed to start PLAYRewards:', error);
            throw error;
        }
    }

    handleUserJoined(data) {
        const userId = data.userId || data.username;
        if (userId) {
            this.viewers.add(userId);
            console.log(`User joined: ${userId}`);
        }
    }

    handleUserLeft(data) {
        const userId = data.userId || data.username;
        if (userId) {
            this.viewers.delete(userId);
            this.players.delete(userId);
            console.log(`User left: ${userId}`);
        }
    }

    handleValidMove(data) {
        const { userId, username, message, timestamp } = data;

        // Track as player (they made a valid move)
        if (!this.players.has(userId)) {
            this.players.set(userId, {
                moves: [],
                joinTime: Date.now(),
                username: username || userId.substring(0, 8)
            });
        }

        this.players.get(userId).moves.push({
            move: message,
            timestamp: timestamp
        });
    }

    startBackgroundTasks() {
        // Example background task: Log stats every 30 seconds
        const statsInterval = setInterval(() => {
            if (!this.isRunning) {
                clearInterval(statsInterval);
                return;
            }
            const stats = this.getStats();
            console.log('Stats:', stats);
        }, 30000);

        this.backgroundTasks.push(statsInterval);

        // Example background task: Cleanup old data every 5 minutes
        const cleanupInterval = setInterval(() => {
            if (!this.isRunning) {
                clearInterval(cleanupInterval);
                return;
            }
            this.cleanupOldData();
        }, 300000); // 5 minutes

        this.backgroundTasks.push(cleanupInterval);
    }

    cleanupOldData() {
        const oneHourAgo = Date.now() - (60 * 60 * 1000);

        // Remove old moves from players
        for (const [userId, playerData] of this.players.entries()) {
            playerData.moves = playerData.moves.filter(move => move.timestamp > oneHourAgo);

            // Remove players with no recent moves
            if (playerData.moves.length === 0 && playerData.joinTime < oneHourAgo) {
                this.players.delete(userId);
            }
        }

        console.log('Cleaned up old data');
    }

    getStats() {
        return {
            totalViewers: this.viewers.size,
            totalPlayers: this.players.size,
            connectedUsers: this.wsClient?.getParticipants().length || 0
        };
    }

    stop() {
        this.isRunning = false;

        // Clear all background tasks
        this.backgroundTasks.forEach(task => {
            if (typeof task === 'number') {
                clearInterval(task);
            }
        });
        this.backgroundTasks = [];

        if (this.wsClient) {
            this.wsClient.disconnect();
        }
        console.log('PLAYRewards system stopped');
    }
}

// Run if this is the main module
if (require.main === module) {
    const rewards = new PLAYRewards();

    rewards.start().then(() => {
        console.log('System running...');

        // Graceful shutdown
        process.on('SIGINT', () => {
            console.log('\nShutting down...');
            rewards.stop();
            process.exit(0);
        });

    }).catch(error => {
        console.error('Failed to start:', error);
        process.exit(1);
    });
}

module.exports = PLAYRewards;