const Database = require('../core/database');

class PumpFunAnalytics {
    constructor() {
        this.database = null;
    }

    async init() {
        this.database = new Database();
        await this.database.init();
    }

    async generateReport(timeframe = '24 HOURS') {
        console.log(`\n📊 PumpFun Analytics Report (Last ${timeframe})`);
        console.log('='.repeat(60));

        // Chat Statistics
        const chatStats = await this.database.getChatStats(timeframe);
        console.log('\n💬 CHAT STATISTICS:');
        console.log(`  Total Messages: ${chatStats.total_messages}`);
        console.log(`  Unique Users: ${chatStats.unique_users}`);
        console.log(`  Valid Moves: ${chatStats.valid_moves}`);
        console.log(`  Invalid Messages: ${chatStats.invalid_messages}`);

        if (chatStats.total_messages > 0) {
            const validMovePercentage = ((chatStats.valid_moves / chatStats.total_messages) * 100).toFixed(1);
            console.log(`  Valid Move Rate: ${validMovePercentage}%`);
        }

        // Top Chatters
        const topChatters = await this.database.getTopChatters(10, timeframe);
        console.log('\n🏆 TOP CHATTERS:');
        topChatters.forEach((chatter, index) => {
            const validRate = chatter.message_count > 0 ?
                ((chatter.valid_moves / chatter.message_count) * 100).toFixed(1) : '0.0';
            console.log(`  ${index + 1}. ${chatter.username}: ${chatter.message_count} messages (${chatter.valid_moves} valid moves, ${validRate}%)`);
        });

        // Viewer Statistics
        const viewerStats = await this.database.getViewerStats(timeframe);
        console.log('\n👥 VIEWER STATISTICS:');
        console.log(`  Total Sessions: ${viewerStats.total_sessions || 0}`);
        console.log(`  Average Viewers: ${viewerStats.avg_viewers ? Math.round(viewerStats.avg_viewers) : 0}`);
        console.log(`  Peak Viewers: ${viewerStats.peak_viewers || 0}`);
        console.log(`  Minimum Viewers: ${viewerStats.min_viewers || 0}`);

        // Most Active Users (combination of chat and viewing)
        const activeUsers = await this.getMostActiveUsers(timeframe);
        console.log('\n⭐ MOST ACTIVE USERS:');
        activeUsers.forEach((user, index) => {
            console.log(`  ${index + 1}. ${user.username}: ${user.total_activity} activities (${user.chat_messages} messages, ${user.viewer_sessions} viewing sessions)`);
        });

        // Recent Activity
        const recentActivity = await this.getRecentActivity(10);
        console.log('\n🕐 RECENT ACTIVITY:');
        recentActivity.forEach(activity => {
            const time = new Date(activity.timestamp).toLocaleTimeString();
            if (activity.type === 'chat') {
                const moveIndicator = activity.is_valid_move ? '🎮' : '💬';
                console.log(`  ${time} ${moveIndicator} ${activity.username}: "${activity.message}"`);
            } else {
                console.log(`  ${time} 👁️  ${activity.username} joined as ${activity.message || 'viewer'}`);
            }
        });

        console.log('\n' + '='.repeat(60));
    }

    async getMostActiveUsers(timeframe = '24 HOURS') {
        const sql = `
            SELECT 
                u.username,
                COALESCE(chat_activity.message_count, 0) as chat_messages,
                COALESCE(viewer_activity.session_count, 0) as viewer_sessions,
                (COALESCE(chat_activity.message_count, 0) + COALESCE(viewer_activity.session_count, 0)) as total_activity
            FROM users u
            LEFT JOIN (
                SELECT user_id, COUNT(*) as message_count
                FROM chat_messages 
                WHERE timestamp >= datetime('now', '-${timeframe}')
                GROUP BY user_id
            ) chat_activity ON u.id = chat_activity.user_id
            LEFT JOIN (
                SELECT user_id, COUNT(DISTINCT scrape_session_id) as session_count
                FROM viewer_sessions 
                WHERE session_timestamp >= datetime('now', '-${timeframe}')
                GROUP BY user_id
            ) viewer_activity ON u.id = viewer_activity.user_id
            WHERE (COALESCE(chat_activity.message_count, 0) + COALESCE(viewer_activity.session_count, 0)) > 0
            ORDER BY total_activity DESC
            LIMIT 10
        `;
        return await this.database.allQuery(sql);
    }

    async getRecentActivity(limit = 20) {
        const sql = `
            SELECT 
                'chat' as type,
                username,
                message,
                is_valid_move,
                timestamp
            FROM chat_messages
            UNION ALL
            SELECT 
                'viewer' as type,
                username,
                user_role as message,
                0 as is_valid_move,
                session_timestamp as timestamp
            FROM viewer_sessions
            ORDER BY timestamp DESC
            LIMIT ?
        `;
        return await this.database.allQuery(sql, [limit]);
    }

    async getHourlyActivity(timeframe = '24 HOURS') {
        const sql = `
            SELECT 
                strftime('%H', timestamp) as hour,
                COUNT(*) as message_count,
                COUNT(CASE WHEN is_valid_move = 1 THEN 1 END) as valid_moves
            FROM chat_messages 
            WHERE timestamp >= datetime('now', '-${timeframe}')
            GROUP BY strftime('%H', timestamp)
            ORDER BY hour
        `;
        return await this.database.allQuery(sql);
    }

    async getUserProfile(username) {
        console.log(`\n👤 USER PROFILE: ${username}`);
        console.log('='.repeat(40));

        // Get user info
        const user = await this.database.getQuery(
            'SELECT * FROM users WHERE username = ?',
            [username]
        );

        if (!user) {
            console.log('❌ User not found');
            return;
        }

        console.log(`  User ID: ${user.id}`);
        console.log(`  Username: ${user.username}`);
        console.log(`  User Address: ${user.user_address || 'N/A'}`);
        console.log(`  Followers: ${user.followers || 'N/A'}`);
        console.log(`  First Seen: ${new Date(user.first_seen).toLocaleString()}`);
        console.log(`  Last Seen: ${new Date(user.last_seen).toLocaleString()}`);

        // Chat activity
        const chatActivity = await this.database.getQuery(`
            SELECT 
                COUNT(*) as total_messages,
                COUNT(CASE WHEN is_valid_move = 1 THEN 1 END) as valid_moves,
                MIN(timestamp) as first_message,
                MAX(timestamp) as last_message
            FROM chat_messages 
            WHERE user_id = ?
        `, [user.id]);

        console.log('\n💬 CHAT ACTIVITY:');
        console.log(`  Total Messages: ${chatActivity.total_messages}`);
        console.log(`  Valid Moves: ${chatActivity.valid_moves}`);
        if (chatActivity.total_messages > 0) {
            const validRate = ((chatActivity.valid_moves / chatActivity.total_messages) * 100).toFixed(1);
            console.log(`  Valid Move Rate: ${validRate}%`);
            console.log(`  First Message: ${new Date(chatActivity.first_message).toLocaleString()}`);
            console.log(`  Last Message: ${new Date(chatActivity.last_message).toLocaleString()}`);
        }

        // Viewing activity
        const viewingActivity = await this.database.allQuery(`
            SELECT 
                user_role,
                COUNT(DISTINCT scrape_session_id) as session_count,
                MIN(session_timestamp) as first_seen,
                MAX(session_timestamp) as last_seen
            FROM viewer_sessions 
            WHERE user_id = ?
            GROUP BY user_role
        `, [user.id]);

        console.log('\n👁️  VIEWING ACTIVITY:');
        if (viewingActivity.length > 0) {
            viewingActivity.forEach(activity => {
                console.log(`  As ${activity.user_role}: ${activity.session_count} sessions`);
                console.log(`    First seen: ${new Date(activity.first_seen).toLocaleString()}`);
                console.log(`    Last seen: ${new Date(activity.last_seen).toLocaleString()}`);
            });
        } else {
            console.log('  No viewing activity recorded');
        }

        // Recent messages
        const recentMessages = await this.database.allQuery(`
            SELECT message, is_valid_move, timestamp
            FROM chat_messages 
            WHERE user_id = ?
            ORDER BY timestamp DESC
            LIMIT 10
        `, [user.id]);

        console.log('\n📝 RECENT MESSAGES:');
        if (recentMessages.length > 0) {
            recentMessages.forEach(msg => {
                const time = new Date(msg.timestamp).toLocaleString();
                const moveIndicator = msg.is_valid_move ? '🎮' : '💬';
                console.log(`  ${time} ${moveIndicator} "${msg.message}"`);
            });
        } else {
            console.log('  No messages recorded');
        }
    }

    async close() {
        if (this.database) {
            await this.database.close();
        }
    }
}

// CLI interface
async function main() {
    const analytics = new PumpFunAnalytics();
    await analytics.init();

    const args = process.argv.slice(2);

    if (args.includes('--user') && args[args.indexOf('--user') + 1]) {
        const username = args[args.indexOf('--user') + 1];
        await analytics.getUserProfile(username);
    } else if (args.includes('--timeframe') && args[args.indexOf('--timeframe') + 1]) {
        const timeframe = args[args.indexOf('--timeframe') + 1];
        await analytics.generateReport(timeframe);
    } else {
        await analytics.generateReport();
    }

    await analytics.close();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = PumpFunAnalytics;
