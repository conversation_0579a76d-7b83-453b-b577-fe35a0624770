This is a Node project that will simply be a script we will run 24/7.
The idea is to add a reflections layer to a PumpFun plays Pokemon livestream, rewarding players and viewers with tokens.

LIVESTREAM_COIN is used for connecting to pump websocket
COIN is used for the reflections
Both use the standard token program
To get user info from a pumpfun name, we can use https://frontend-api-v3.pump.fun/users/ as PUMP_USER_INFO_URL in the dotenv.
you append the username after /users. Moreover, if the user doesnt have a Name set, it will simply be their wallet public key, and we can use that.

1. We can use the PumpFun websocket to retrieve data. There is a python example in websocket.md, PumpFun uses LiveKit.
2. We keep track of participants Joining and Leaving, plus entering correct moves in the chat (up, down, etc)
3. Using the pumpswap sdk (@pump-fun/pump-swap-sdk) we can check the vault balance for fees we've earned
4. We give 60% to the players/viewers, while keeping 40% for bounties. From the 60%, 70% goes to the players, 30% to the viewers. Viewer+Player does not stack.
5. Using the vault balance change from the past hour (we claim hourly) we can calculate the amount of tokens to give out.
6. Check if nothing is over the max allowed value in .env, if so, clamp it.
7. If our coin balance is high enough, and price is up from the last hour, we can use our existing COIN balance to send tokens
8. If its not high enough, or the price is down, we buy the necessary amount +5%
9. We start iterating to send out transactions here. For every batch (multiple sends in one tx) we check:
10. Does the user have 50K COIN in their wallet and did they play/watch? Then they are  eligible. If not, skip.
11. Send out the transactions.