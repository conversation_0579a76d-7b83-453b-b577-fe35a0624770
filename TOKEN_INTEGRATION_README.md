# Token Distribution Integration

This document describes the complete integration of the token distribution system with the reward calculator.

## 🎯 Overview

The integrated system now performs the complete flow from reward calculation to token distribution:

1. **Reward Calculation**: Calculates SOL rewards based on player moves and viewer activity
2. **Vault Claiming**: Claims SOL from the creator vault using Pump SDK
3. **Token Swapping**: Swaps SOL to reward tokens via Jupiter with balance tracking
4. **Token Distribution**: Distributes tokens to eligible users with batching and retry logic
5. **Database Storage**: Stores all results and transaction data in SQLite

## 🔧 Key Features

### Balance Tracking
- **Before Swap**: Gets token balance before swapping SOL
- **After Swap**: Gets token balance after swapping SOL  
- **Actual Distribution**: Uses the difference as the final amount to distribute

### Database Integration
- **New Tables**: `token_distributions` and `token_transfer_results`
- **Complete Tracking**: Stores swap details, transfer results, and failure reasons
- **Transaction Links**: Stores Solana transaction IDs for verification

### Error Handling
- **Two-Phase Distribution**: First run without validation, second run with token account validation
- **Retry Logic**: Automatic retries for failed transactions
- **Comprehensive Logging**: Detailed logs for debugging and monitoring

## 📁 Files Modified

### Core Integration
- `src/core/token-distributor.js`: Added `getTokenBalance()`, `distributeRewards()`, and database storage
- `rewards-processor.js`: Integrated TokenDistributor and updated summary output
- `token-rewards-processor.js`: New unified entry point

### Key Methods Added

#### TokenDistributor
- `getTokenBalance()`: Gets current token balance (always 6 decimals)
- `distributeRewards()`: Main integration method that handles the complete flow
- `convertSOLRewardsToTokens()`: Converts SOL amounts to token amounts based on actual received tokens
- `storeDistributionResults()`: Stores all distribution data in database
- `printDistributionSummary()`: Prints comprehensive results

## 🚀 Usage

### Run Complete Token Distribution
```bash
node token-rewards-processor.js
```

This will:
1. Calculate rewards based on recent activity
2. Claim SOL from vault
3. Swap SOL to tokens
4. Distribute tokens to users
5. Store all results in database

### Environment Variables Required
```bash
# Solana Configuration
RPC_URL=https://api.mainnet-beta.solana.com
PK=your_private_key_in_base58
COIN=your_token_mint_address

# Reward Limits
MAX_HUNTER_REWARD_VALUE_USD=100
MAX_VIEWER_REWARD_VALUE_USD=50

# Token Balance Requirements
MINIMUM_TOKEN_BALANCE_UI=1000
```

## 📊 Database Schema

### token_distributions
Tracks each distribution run:
- `sol_amount_swapped`: Amount of SOL swapped
- `tokens_received`: Actual tokens received from swap
- `token_price_usd`: Token price at time of distribution
- `balance_before_swap` / `balance_after_swap`: Balance tracking
- Transaction IDs and summary statistics

### token_transfer_results  
Tracks individual user transfers:
- User details and token account addresses
- Token amounts and USD values
- Transfer status (success/failed) and transaction IDs
- Failure reasons for debugging

## 🔍 Monitoring

The system provides comprehensive logging:
- **Swap Details**: SOL amount, tokens received, price
- **Distribution Progress**: Batch creation and execution
- **Success/Failure Tracking**: Individual transfer results
- **Database Confirmation**: Storage verification

## ⚠️ Important Notes

1. **Balance Precision**: All token amounts use 6 decimals as specified
2. **USD Limits**: Rewards are capped at MAX_HUNTER_REWARD_VALUE_USD and MAX_VIEWER_REWARD_VALUE_USD
3. **Two-Phase Distribution**: Handles token account creation issues gracefully
4. **Transaction Batching**: Optimizes for Solana transaction size limits

## 🎉 Integration Complete

The system is now fully integrated and ready for production use. All reward calculation, token swapping, distribution, and database storage is handled automatically in a single process.
