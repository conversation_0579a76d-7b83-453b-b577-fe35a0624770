require('dotenv').config();
const RewardsProcessor = require('./rewards-processor');

/**
 * Integrated Token Rewards Processor
 * 
 * This script combines the reward calculation and token distribution into a single process:
 * 1. Calculates SOL rewards based on player moves and viewer activity
 * 2. <PERSON>laims <PERSON><PERSON> from the creator vault
 * 3. Swaps SOL to reward tokens via Jupiter
 * 4. Distributes tokens to eligible users
 * 5. Stores all results in the database
 */

async function main() {
    console.log('🚀 Starting Integrated Token Rewards Processor...');
    console.log('='.repeat(60));

    const processor = new RewardsProcessor();

    try {
        // Initialize the processor (includes TokenDistributor)
        await processor.init();

        // Process rewards and distribute tokens
        await processor.processRewards();

        console.log('\n🎉 Token rewards processing completed successfully!');

    } catch (error) {
        console.error('💥 Fatal error in token rewards processing:', error);
        process.exit(1);
    } finally {
        await processor.close();
    }
}

// Main loop ( every 60 minutes )
async function main_loop() {
    while (true) {
        try {
            await main();
        } catch (error) {
            console.error('💥 Fatal error in token rewards processing:', error);
        }
        console.log('🔄 Waiting 60 minutes before next run...');
        await new Promise(resolve => setTimeout(resolve, 60 * 60 * 1000));
    }
}
// Run if this file is executed directly
if (require.main === module) {
    main_loop().catch(error => {
        console.error('💥 Unexpected error:', error);
        process.exit(1);
    });
}

module.exports = { main };
