require('dotenv').config();
const { Keypair } = require('@solana/web3.js');
const TokenDistributor = require('./src/core/token-distributor');
const bs58 = require('bs58').default;

async function testTokenDistribution() {
    console.log('🧪 Starting Token Distributor Test...\n');

    try {
        // Initialize the distributor
        const distributor = new TokenDistributor();
        await distributor.init();

        // get pk from env
        const pk = process.env.PK;
        const privateKeyBuffer = bs58.decode(pk);
        const keypair = Keypair.fromSecretKey(privateKeyBuffer);

        // Test data: FKWsQNbuTN3bXY3mTra66nNb8x61wDTKcdQYf184mUk1 receives 1% of rewards 100 times
        const testWallet = 'FKWsQNbuTN3bXY3mTra66nNb8x61wDTKcdQYf184mUk1';
        const testTokenAccount = '4Ebcm2jSeRDZeRV13bmzvwgk7rGYMXU19qbPq9qEW6nG';

        // Simulate reward data - 100 recipients, each getting 1% of total rewards
        const totalRewardTokens = 100000 * 1e6; // 100K tokens with 6 decimals
        const rewardPerRecipient = Math.floor(totalRewardTokens * 0.01); // 1% each

        console.log(`📊 Test Parameters:`);
        console.log(`   Total reward pool: ${(totalRewardTokens / 1e6).toLocaleString()} tokens`);
        console.log(`   Reward per recipient: ${(rewardPerRecipient / 1e6).toLocaleString()} tokens (1%)`);
        console.log(`   Number of recipients: 100`);
        console.log(`   Test wallet: ${testWallet}`);
        console.log(`   Test token account: ${testTokenAccount}\n`);

        // Create test recipients
        const recipients = [];
        for (let i = 1; i <= 100; i++) {
            recipients.push({
                username: `TestUser${i}`,
                userAddress: testWallet,
                tokenAccountAddress: testTokenAccount,
                rewardType: i <= 70 ? 'player' : 'viewer', // 70 players, 30 viewers
                tokenAmount: rewardPerRecipient,
                rank: i
            });
        }

        console.log(`👥 Created ${recipients.length} test recipients`);
        console.log(`   Players: ${recipients.filter(r => r.rewardType === 'player').length}`);
        console.log(`   Viewers: ${recipients.filter(r => r.rewardType === 'viewer').length}\n`);

        // Step 1: Vault claiming
        console.log('🏦 Step 1: Claiming from vault...');
        const vaultClaimResult = await distributor.claimFromVault();
        console.log(`✅ Vault claim result:`, vaultClaimResult);

        // Swap SOL to tokens
        console.log('🔄 Step 2: Swapping SOL to reward tokens...')
        const swapResult = await distributor.swapSOLToToken(0.01);

        // 3 Get token price
        const tokenPrice = await distributor.getTokenPrice(process.env.COIN);
        console.log(`   Token price: $${tokenPrice}`);

        // Step 4: Validate reward amounts against USD limits
        console.log('🔍 Step 4: Validating reward amounts...');
        const validatedRecipients = distributor.validateRewardAmounts(recipients, tokenPrice);
        const cappedCount = validatedRecipients.filter(r => r.capped).length;
        console.log(`✅ Validation complete: ${cappedCount} rewards were capped`);
        console.log();

        // Step 5: Create transfer batches (first run - no token account validation)
        console.log('📦 Step 5: Creating transfer batches (first run)...');
        const batches = await distributor.createTransferBatches(validatedRecipients, false);
        console.log(`✅ Created ${batches.length} batches`);

        const successBatches = batches.filter(b => b.type === 'success');
        const failedBatches = batches.filter(b => b.type === 'failed');
        console.log(`   Success batches: ${successBatches.length}`);
        console.log(`   Failed recipients: ${failedBatches.length}`);

        // Show batch details
        successBatches.forEach((batch, index) => {
            console.log(`   Batch ${index + 1}: ${batch.recipients.length} recipients, ~${batch.estimatedSize} bytes`);
        });
        console.log();

        // Step 6: Batch execution
        console.log('🚀 Step 6: Executing transfer batches (first run)...')
        const results = await distributor.executeBatches(batches);

        console.log(`✅ Sending complete:`);
        const successfulTxs = results.filter(r => r.type === 'success').length;
        const failedRecipients = results.filter(r => r.type === 'failed').length;
        console.log(`   Successful transactions: ${successfulTxs}`);
        console.log(`   Failed recipients: ${failedRecipients}`);

        // Show transaction IDs
        results.filter(r => r.type === 'success').forEach((result, index) => {
            console.log(`   TX ${index + 1}: ${result.txid} (${result.recipients.length} recipients)`);
        });
        console.log();

        // Step 7: Second run with token account validation
        console.log('🔍 Step 7: Simulating second run with token account validation...');
        const secondRunBatches = await distributor.createTransferBatches(
            validatedRecipients.filter(r => !r.processed), // Only unprocessed recipients
            true // Enable token account validation
        );

        // Step 8: Execute second run batches
        console.log('🚀 Step 8: Executing second run batches...')
        const secondRunResults = await distributor.executeBatches(secondRunBatches);

        // Step 9: Process results and print summary
        console.log('🎉 Step 9: Processing results and printing summary...')
        const secondRunSuccess = secondRunResults.filter(r => r.type === 'success').length;

        const secondRunFailed = secondRunResults.filter(r => r.type === 'failed').length;
        console.log(`   Second run successful: ${secondRunSuccess}`);
        console.log(`   Second run failed: ${secondRunFailed}`);
        console.log(`   Total successful: ${successfulTxs + secondRunSuccess}`);
        console.log(`   Total failed: ${failedRecipients + secondRunFailed}`);
        console.log(`   Complete!`);


        // Return structured data for integration with rewards-processor
        return {
            success: true,
            vaultClaim: vaultClaimResult,
            swap: swapResult,
            tokenPrice: tokenPrice,
            recipients: validatedRecipients,
            firstRunResults: simulatedResults,
            secondRunResults: secondRunBatches,
            summary: {
                totalRecipients: recipients.length,
                cappedRewards: cappedCount,
                firstRunSuccessful: successfulTxs,
                firstRunFailed: failedRecipients,
                secondRunSuccessful: secondRunSuccess,
                secondRunFailed: secondRunFailed
            }
        };

    } catch (error) {
        console.error('❌ Test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testTokenDistribution()
        .then(result => {
            if (result.success) {
                console.log('🎉 Token Distributor test completed successfully!');
            } else {
                console.log('💥 Token Distributor test failed:', result.error);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 Unexpected error:', error);
            process.exit(1);
        });
}

module.exports = { testTokenDistribution };
