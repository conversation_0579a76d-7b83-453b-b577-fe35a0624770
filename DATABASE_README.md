# PumpFun Database Integration

This project now includes comprehensive SQLite database integration to store and analyze viewer and chat data from your PumpFun livestream.

## 🗃️ Database Schema

### Tables

1. **`users`** - Central user registry
   - `id` (Primary Key)
   - `username` (Unique)
   - `user_address` (Wallet address if available)
   - `followers` (Follower count string)
   - `first_seen`, `last_seen` (Timestamps)

2. **`chat_messages`** - All chat messages
   - `id` (Primary Key)
   - `user_id` (Foreign Key to users)
   - `username`, `user_address`, `message`
   - `is_valid_move` (Boolean - true for valid Pokemon moves)
   - `timestamp` (Message timestamp)

3. **`viewer_sessions`** - Viewer data from scraping
   - `id` (Primary Key)
   - `user_id` (Foreign Key to users)
   - `username`, `user_role` (host/moderator/viewer)
   - `followers`, `is_you` (Boolean)
   - `session_timestamp`, `scrape_session_id`

4. **`scrape_sessions`** - Metadata for each scraping run
   - `id` (Primary Key - session identifier)
   - `total_hosts`, `total_moderators`, `total_viewers`
   - `timestamp`

### Indexes

Optimized indexes are automatically created for:
- User lookups (username, user_address)
- Time-based queries (timestamps)
- Valid move filtering
- Session grouping

## 🚀 Usage

### 1. Basic Database Operations

```javascript
const Database = require('./database');

const db = new Database();
await db.init();

// Insert a chat message
await db.insertChatMessage({
    username: 'player1',
    userAddress: '0x123...abc',
    message: 'up',
    timestamp: Date.now()
});

// Insert viewer session data
await db.insertViewerSession({
    participants: {
        host: [{ username: 'streamer', followers: '1k', isYou: false }],
        moderators: [],
        viewers: [{ username: 'viewer1', followers: '100', isYou: false }]
    },
    timestamp: new Date().toISOString(),
    sessionId: 'unique_session_id'
});

await db.close();
```

### 2. WebSocket Client with Database

```javascript
const PumpFunWebSocketClient = require('./websocket-client');

const client = new PumpFunWebSocketClient('your-room-id', true); // Enable database
await client.initDatabase();
await client.connect();

// Messages are automatically stored in the database
```

### 3. Viewer Scraper with Database

```javascript
const { PumpFunViewerScraper } = require('./pumpfun-viewer-scraper');

const scraper = new PumpFunViewerScraper(true); // Enable database
await scraper.init();
await scraper.initDatabase();

// Viewer data is automatically stored when scraping
```

## 📊 Analytics

### Generate Reports

```bash
# Generate 24-hour report
node analytics.js

# Custom timeframe
node analytics.js --timeframe "7 DAYS"

# User profile
node analytics.js --user "player1"
```

### Available Analytics

- **Chat Statistics**: Total messages, unique users, valid moves
- **Top Chatters**: Most active users with valid move rates
- **Viewer Statistics**: Session counts, peak viewers, averages
- **User Profiles**: Individual user activity and history
- **Recent Activity**: Latest chat and viewing activity

## 🛠️ Example Scripts

### Demo Database Operations
```bash
node example-with-database.js --demo-db
```

### Start WebSocket with Database
```bash
node example-with-database.js --websocket
```

### Run Analytics
```bash
node analytics.js
```

## 📈 Key Features

### Automatic User Management
- Users are automatically created/updated when they chat or appear in viewer lists
- Deduplication based on username and wallet address
- Tracks first seen, last seen timestamps

### Valid Move Detection
- Automatically identifies valid Pokemon moves: `up`, `down`, `left`, `right`, `a`, `b`, `start`, `select`
- Calculates valid move rates for users and overall statistics

### Optimized Performance
- Strategic indexes for fast queries
- Efficient upsert operations
- Batch processing support

### Comprehensive Analytics
- Time-based filtering (hours, days, weeks)
- User activity profiles
- Engagement metrics
- Real-time activity feeds

## 🔧 Configuration

### Database Location
Default: `./pumpfun_data.db`

To use a custom location:
```javascript
const db = new Database('/path/to/your/database.db');
```

### Disable Database
To run without database integration:
```javascript
const client = new PumpFunWebSocketClient('room-id', false); // Disable database
const scraper = new PumpFunViewerScraper(false); // Disable database
```

## 📋 Database Maintenance

### Backup
```bash
cp pumpfun_data.db pumpfun_data_backup_$(date +%Y%m%d).db
```

### View Database
Use any SQLite browser or:
```bash
sqlite3 pumpfun_data.db
.tables
.schema users
SELECT * FROM users LIMIT 10;
```

## 🎯 Next Steps

1. **Run the demo**: `node example-with-database.js --demo-db`
2. **Start collecting data**: Update your room ID and run the WebSocket client
3. **Monitor analytics**: Use `node analytics.js` to view insights
4. **Customize queries**: Extend the Database class for specific analytics needs

The database will automatically handle all data storage as your scripts run, providing you with comprehensive insights into your PumpFun livestream engagement!
