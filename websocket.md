 async with websockets.connect(
            ws_url,
            additional_headers={
                "Origin": "https://pump.fun",
                "User-Agent": "Mozilla/5.0",
            }
        ) as ws:
            print("Connected to pump.fun chat")
            
            # Timer for move intervals and mode swaps
            last_move_check = time.time()
            last_mode_check = time.time()
            
            async for msg in ws:
                # Handle ping/pong
                if msg == "2":
                    await ws.send("3")
                    continue
                
                # Initial handshake
                if msg.startswith("0"):
                    connect_payload = {
                        "origin": "https://pump.fun",
                        "timestamp": int(time.time() * 1000),
                        "token": None,
                    }
                    await ws.send("40" + json.dumps(connect_payload))
                
                # Join room
                if msg.startswith("40"):
                    join_payload = [
                        "joinRoom",
                        {"roomId": self.room_id, "username": ""}
                    ]
                    await ws.send("42" + json.dumps(join_payload))
                
                # Process messages
                if msg.startswith("42"):
                    try:
                        data = json.loads(msg[2:])
                        self.process_message(data)
                    except Exception as e:
                        print(f"Parse error: {e}")
                
                # Update timers
                current_time = time.time()
                self.move_timer = int(current_time - last_move_check)
                self.mode_timer = int(current_time - last_mode_check)
                
                # Check for mode swap based on current mode duration
                current_mode_duration = self.get_current_mode_duration()
                if self.mode_timer >= current_mode_duration:
                    self.swap_mode()
                    last_mode_check = current_time
                
                # Check for moves based on timer
                winning_move = self.get_winning_move()
                if winning_move:
                    self.execute_move(winning_move)
                    last_move_check = current_time